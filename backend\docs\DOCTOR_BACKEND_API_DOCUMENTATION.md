# Doctor Backend API Documentation

## Overview
This document covers all backend APIs and functionality for doctor-related features in the Healthwise Patient Hub system.

## Base URL
```
http://localhost:5000/api
```

## Authentication
All doctor APIs require authentication using Bear<PERSON> token:
```
Authorization: Bearer <jwt_token>
```

## Doctor Role Verification
All doctor endpoints include middleware to ensure only users with `role: 'doctor'` can access them.

---

## 1. DOCTOR DASHBOARD APIs

### Base Route: `/api/doctor/dashboard`

#### GET `/api/doctor/dashboard/stats`
**Purpose:** Get dashboard statistics for the logged-in doctor
**Response:**
```json
{
  "success": true,
  "data": {
    "totalPatients": 25,
    "totalAppointments": 45,
    "totalReports": 30,
    "pendingAppointments": 8,
    "completedAppointments": 37,
    "recentActivity": [...]
  }
}
```

---

## 2. DOCTOR PROFILE APIs

### Base Route: `/api/doctor/profile`

#### GET `/api/doctor/profile`
**Purpose:** Get doctor's profile information
**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "doctor_id",
    "name": "Dr. <PERSON> Doe",
    "email": "<EMAIL>",
    "specialization": "Cardiology",
    "experience": "10 years",
    "phone": "+**********"
  }
}
```

#### PUT `/api/doctor/profile`
**Purpose:** Update doctor's profile
**Request Body:**
```json
{
  "name": "Dr. John Doe",
  "specialization": "Cardiology",
  "experience": "10 years",
  "phone": "+**********"
}
```

---

## 3. DOCTOR PATIENTS APIs

### Base Route: `/api/doctor/patients`

#### GET `/api/doctor/patients`
**Purpose:** Get all patients associated with the doctor
**Query Parameters:**
- `search` - Search by patient name or email
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "patient_id",
      "name": "John Smith",
      "email": "<EMAIL>",
      "phone": "+**********",
      "age": 35,
      "gender": "male",
      "address": "123 Main St"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "totalPatients": 25
  }
}
```

#### GET `/api/doctor/patients/:patientId`
**Purpose:** Get detailed information about a specific patient

---

## 4. DOCTOR REPORTS APIs

### Base Route: `/api/doctor/reports`

#### GET `/api/doctor/reports`
**Purpose:** Get all reports created by the logged-in doctor
**Query Parameters:**
- `search` - Search by report type, patient name, or email
- `patientId` - Filter by specific patient
- `status` - Filter by status (pending, completed, reviewed)
- `type` - Filter by report type
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "report_id",
      "type": "Blood Test",
      "date": "2025-01-15T00:00:00.000Z",
      "status": "completed",
      "pdfPath": "/uploads/report_123.pdf",
      "patient": {
        "_id": "patient_id",
        "name": "John Smith",
        "email": "<EMAIL>",
        "phone": "+**********",
        "age": 35,
        "gender": "male"
      },
      "doctor": {
        "_id": "doctor_id",
        "name": "Dr. Jane Doe",
        "email": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalReports": 50,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### GET `/api/doctor/reports/stats`
**Purpose:** Get report statistics for dashboard
**Response:**
```json
{
  "success": true,
  "data": {
    "total": 50,
    "pending": 10,
    "completed": 35,
    "reviewed": 5,
    "latest": [
      {
        "_id": "report_id",
        "type": "X-Ray",
        "date": "2025-01-15T00:00:00.000Z",
        "status": "pending",
        "patientName": "John Smith"
      }
    ],
    "typeBreakdown": [
      { "_id": "Blood Test", "count": 15 },
      { "_id": "X-Ray", "count": 12 }
    ]
  }
}
```

#### GET `/api/doctor/reports/:reportId`
**Purpose:** Get detailed information about a specific report
**Security:** Only returns reports created by the logged-in doctor

#### GET `/api/doctor/reports/:reportId/download`
**Purpose:** Download report PDF file
**Response:** PDF file download
**Headers:**
```
Content-Type: application/pdf
Content-Disposition: attachment; filename="PatientName_ReportType_Date.pdf"
```

#### GET `/api/doctor/reports/:reportId/view`
**Purpose:** View report PDF in browser
**Response:** PDF file for inline viewing
**Headers:**
```
Content-Type: application/pdf
Content-Disposition: inline
```

---

## 5. INDIVIDUAL REPORT API

### Base Route: `/api/doctor/report`

#### GET `/api/doctor/report/:reportId`
**Purpose:** Get single report details (alternative endpoint)
**Note:** This is a legacy endpoint, use `/api/doctor/reports/:reportId` instead

---

## Security Features

### 1. Authentication Middleware
- All routes require valid JWT token
- Token must be provided in Authorization header

### 2. Role-Based Access Control
- All doctor routes verify `user.role === 'doctor'`
- Unauthorized access returns 403 Forbidden

### 3. Data Ownership Verification
- Doctors can only access their own reports
- Patient data is filtered by doctor association
- Report access is restricted to creator doctor

### 4. Input Validation
- All endpoints validate required parameters
- Search queries are sanitized
- Pagination limits are enforced

---

## Error Responses

### Common Error Formats
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message (development only)"
}
```

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error

---

## File Structure

```
backend/
├── controllers/doctor/
│   ├── dashboardController.js
│   ├── profileController.js
│   ├── patientController.js
│   ├── reportController.js (legacy)
│   └── reportsController.js (main)
├── routes/doctor/
│   ├── dashboardRoutes.js
│   ├── profileRoutes.js
│   ├── patientRoutes.js
│   ├── reportRoutes.js (legacy)
│   └── reportsRoutes.js (main)
├── middleware/
│   └── authMiddleware.js
└── models/doctor/
    ├── PatientRecord.js
    └── Report.js
```

---

## Usage Examples

### Login and Get Reports
```javascript
// 1. Login
const loginResponse = await fetch('/api/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});
const { token } = await loginResponse.json();

// 2. Get Reports
const reportsResponse = await fetch('/api/doctor/reports', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const reports = await reportsResponse.json();
```

### Search Reports
```javascript
const searchResponse = await fetch('/api/doctor/reports?search=blood&status=pending', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Download Report
```javascript
const downloadResponse = await fetch(`/api/doctor/reports/${reportId}/download`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
const blob = await downloadResponse.blob();
// Handle blob download
```

---

## Notes

1. **Main Reports API:** Use `/api/doctor/reports` for all report operations
2. **Legacy Support:** `/api/doctor/report` exists for backward compatibility
3. **File Uploads:** PDF files are stored in `/uploads/` directory
4. **Database Models:** Reports reference PatientRecord via ObjectId
5. **Search Functionality:** Supports multi-field search across report types and patient data
