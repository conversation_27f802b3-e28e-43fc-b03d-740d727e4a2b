
import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ScheduleAppointmentModal from '@/components/ScheduleAppointmentModal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { appointmentService, AppointmentData } from '@/services/appointmentService';
import {
  CalendarClock,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Loader2,
  Calendar as CalendarIcon,
  Stethoscope,
  AlertCircle
} from 'lucide-react';

const AppointmentsPage = () => {
  const { user, token } = useAuth();
  const { toast } = useToast();

  // State management
  const [appointments, setAppointments] = useState<AppointmentData[]>([]);
  const [loading, setLoading] = useState(false);
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('upcoming');

  // Load appointments when component mounts
  useEffect(() => {
    if (user && token) {
      fetchAppointments();
    }
  }, [user, token]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const response = await appointmentService.getAppointments(token!, {
        page: 1,
        limit: 50
      });
      setAppointments(response.appointments);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      toast({
        title: "Error",
        description: "Failed to load appointments",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAppointmentScheduled = () => {
    fetchAppointments(); // Refresh appointments list
    toast({
      title: "Success",
      description: "Appointment scheduled successfully",
    });
  };

  const handleCancelAppointment = async (appointmentId: string) => {
    try {
      await appointmentService.cancelAppointment(token!, appointmentId, 'Cancelled by user');
      await fetchAppointments(); // Refresh the list
      toast({
        title: "Success",
        description: "Appointment cancelled successfully",
      });
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      toast({
        title: "Error",
        description: "Failed to cancel appointment",
        variant: "destructive",
      });
    }
  };

  // Helper functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
      case 'confirmed':
        return <CalendarIcon className="h-4 w-4 text-blue-600" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };
  
  // Filter appointments based on status
  const upcomingAppointments = appointments.filter(apt =>
    ['scheduled', 'confirmed', 'pending'].includes(apt.status) &&
    new Date(apt.dateTime) > new Date()
  );

  const pastAppointments = appointments.filter(apt =>
    apt.status === 'completed' ||
    apt.status === 'cancelled' ||
    new Date(apt.dateTime) <= new Date()
  );
  
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Appointments</h1>
            <p className="text-muted-foreground">Schedule and manage your medical appointments</p>
          </div>
          <Button onClick={() => setIsScheduleModalOpen(true)} className="gap-2">
            <CalendarClock size={16} />
            Schedule New Appointment
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming Appointments</TabsTrigger>
            <TabsTrigger value="past">Past Appointments</TabsTrigger>
          </TabsList>
          
          {/* Upcoming Appointments Tab */}
          <TabsContent value="upcoming">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Upcoming Appointments
                </CardTitle>
                <CardDescription>
                  Your scheduled appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading appointments...</span>
                  </div>
                ) : upcomingAppointments.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingAppointments.map((appointment) => (
                      <Card key={appointment._id} className="overflow-hidden">
                        <div className="flex flex-col md:flex-row">
                          <div className="bg-primary p-4 flex flex-col justify-center items-center text-primary-foreground md:w-1/4">
                            <CalendarIcon className="h-8 w-8 mb-2" />
                            <div className="text-center">
                              <p className="font-bold text-lg">
                                {new Date(appointment.dateTime).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric'
                                })}
                              </p>
                              <p className="text-sm opacity-90">
                                {new Date(appointment.dateTime).toLocaleDateString('en-US', {
                                  weekday: 'long'
                                })}
                              </p>
                              <p className="text-lg mt-1">{formatTime(appointment.dateTime)}</p>
                            </div>
                          </div>

                          <div className="p-4 flex-1">
                            <div className="flex justify-between items-start mb-3">
                              <div>
                                <h3 className="font-semibold text-lg flex items-center gap-2">
                                  <Stethoscope className="h-4 w-4" />
                                  {user?.role === 'patient'
                                    ? appointment.doctorId.name
                                    : appointment.patientId.name
                                  }
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                  {appointment.type} • {appointment.duration} minutes
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                {getStatusIcon(appointment.status)}
                                {getStatusBadge(appointment.status)}
                              </div>
                            </div>

                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium">Reason:</span> {appointment.reason}
                              </div>
                              {appointment.symptoms && (
                                <div>
                                  <span className="font-medium">Symptoms:</span> {appointment.symptoms}
                                </div>
                              )}
                              {appointment.notes && (
                                <div>
                                  <span className="font-medium">Notes:</span> {appointment.notes}
                                </div>
                              )}
                            </div>

                            <div className="mt-4 flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // View details functionality can be added here
                                }}
                              >
                                <FileText className="h-4 w-4 mr-1" />
                                View Details
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleCancelAppointment(appointment._id)}
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No upcoming appointments</h3>
                    <p className="text-muted-foreground mb-4">
                      You don't have any scheduled appointments yet.
                    </p>
                    <Button onClick={() => setIsScheduleModalOpen(true)}>
                      <CalendarClock className="h-4 w-4 mr-2" />
                      Schedule Your First Appointment
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Past Appointments Tab */}
          <TabsContent value="past">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Past Appointments
                </CardTitle>
                <CardDescription>
                  Your completed and cancelled appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading appointments...</span>
                  </div>
                ) : pastAppointments.length > 0 ? (
                  <div className="space-y-4">
                    {pastAppointments.map((appointment) => (
                      <Card key={appointment._id} className="overflow-hidden opacity-75">
                        <div className="flex flex-col md:flex-row">
                          <div className="bg-muted p-4 flex flex-col justify-center items-center md:w-1/4">
                            <CalendarIcon className="h-8 w-8 mb-2 text-muted-foreground" />
                            <div className="text-center">
                              <p className="font-bold text-lg">
                                {new Date(appointment.dateTime).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric'
                                })}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(appointment.dateTime).toLocaleDateString('en-US', {
                                  weekday: 'long'
                                })}
                              </p>
                              <p className="text-lg mt-1">{formatTime(appointment.dateTime)}</p>
                            </div>
                          </div>

                          <div className="p-4 flex-1">
                            <div className="flex justify-between items-start mb-3">
                              <div>
                                <h3 className="font-semibold text-lg flex items-center gap-2">
                                  <Stethoscope className="h-4 w-4" />
                                  {user?.role === 'patient'
                                    ? appointment.doctorId.name
                                    : appointment.patientId.name
                                  }
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                  {appointment.type} • {appointment.duration} minutes
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                {getStatusIcon(appointment.status)}
                                {getStatusBadge(appointment.status)}
                              </div>
                            </div>

                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="font-medium">Reason:</span> {appointment.reason}
                              </div>
                              {appointment.symptoms && (
                                <div>
                                  <span className="font-medium">Symptoms:</span> {appointment.symptoms}
                                </div>
                              )}
                              {appointment.doctorNotes && (
                                <div>
                                  <span className="font-medium">Doctor's Notes:</span> {appointment.doctorNotes}
                                </div>
                              )}
                              {appointment.notes && (
                                <div>
                                  <span className="font-medium">Notes:</span> {appointment.notes}
                                </div>
                              )}
                            </div>

                            <div className="mt-4 flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // View details functionality can be added here
                                }}
                              >
                                <FileText className="h-4 w-4 mr-1" />
                                View Details
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No past appointments</h3>
                    <p className="text-muted-foreground">
                      Your completed and cancelled appointments will appear here.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Schedule Appointment Modal */}
        <ScheduleAppointmentModal
          isOpen={isScheduleModalOpen}
          onClose={() => setIsScheduleModalOpen(false)}
          onAppointmentScheduled={handleAppointmentScheduled}
        />
      </div>
    </DashboardLayout>
  );
};

export default AppointmentsPage;
                        
                        <div>
                          <h3 className="font-semibold mb-2">Status</h3>
                          <div className="flex items-center gap-2">
                            {selectedAppointment.status === 'scheduled' && (
                              <ClockIcon className="h-4 w-4 text-blue-500" />
                            )}
                            {selectedAppointment.status === 'completed' && (
                              <Check className="h-4 w-4 text-green-500" />
                            )}
                            {selectedAppointment.status === 'cancelled' && (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span>{selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}</span>
                          </div>
                        </div>
                        
                        {selectedAppointment.notes && (
                          <div>
                            <h3 className="font-semibold mb-2">Notes</h3>
                            <p className="text-sm">{selectedAppointment.notes}</p>
                          </div>
                        )}
                        
                        <div>
                          <h3 className="font-semibold mb-2">Reminders</h3>
                          <div className="space-y-2">
                            {getAppointmentReminders(selectedAppointment.id).length > 0 ? (
                              getAppointmentReminders(selectedAppointment.id).map(reminder => (
                                <div key={reminder.id} className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <Bell className="h-4 w-4 text-muted-foreground" />
                                    <span>{reminder.time}</span>
                                  </div>
                                  <Badge variant={reminder.enabled ? "default" : "outline"}>
                                    {reminder.enabled ? "Enabled" : "Disabled"}
                                  </Badge>
                                </div>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground">No reminders set</p>
                            )}
                          </div>
                        </div>
                        
                        {selectedAppointment.status === 'scheduled' && !isDateInPast(selectedAppointment.dateTime) && (
                          <Button 
                            variant="destructive" 
                            className="w-full mt-4"
                            onClick={() => handleCancelAppointment(selectedAppointment.id)}
                          >
                            Cancel Appointment
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-12 text-center">
                        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Appointment Selected</h3>
                        <p className="text-muted-foreground">
                          Select an appointment to view details
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
          
          {/* Past Appointments Tab */}
          <TabsContent value="past">
            <Card>
              <CardHeader>
                <CardTitle>Past Appointments</CardTitle>
                <CardDescription>
                  Your completed and cancelled appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                {getPastAppointments().length > 0 ? (
                  <div className="space-y-4">
                    {getPastAppointments().map((appointment) => {
                      const doctor = getDoctorById(appointment.doctorId);
                      
                      return (
                        <div 
                          key={appointment.id} 
                          className="flex flex-col md:flex-row border rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                          onClick={() => setSelectedAppointment(appointment)}
                        >
                          <div className={`p-4 md:w-32 flex flex-col justify-center items-center text-white ${
                            appointment.status === 'completed' 
                              ? 'bg-green-500' 
                              : appointment.status === 'cancelled' 
                              ? 'bg-red-500' 
                              : 'bg-yellow-500'
                          }`}>
                            <p className="font-bold text-lg">{new Date(appointment.dateTime).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</p>
                            <p className="text-sm">{formatTime(appointment.dateTime)}</p>
                          </div>
                          
                          <div className="p-4 flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-medium">Dr. {doctor?.name}</h3>
                                <p className="text-sm text-muted-foreground">{doctor?.specialty}</p>
                              </div>
                              {getAppointmentStatusBadge(appointment.status)}
                            </div>
                            
                            {appointment.notes && (
                              <div className="mt-2 text-sm">
                                <span className="font-medium">Notes:</span> {appointment.notes}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <CalendarIcon className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Past Appointments</h3>
                    <p className="text-muted-foreground">
                      You don't have any past appointments
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Schedule Appointment Tab */}
          <TabsContent value="schedule">
            <Card>
              <CardHeader>
                <CardTitle>Schedule New Appointment</CardTitle>
                <CardDescription>
                  Book an appointment with a healthcare provider
                </CardDescription>
              </CardHeader>
              <CardContent>
                {step === 1 && (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="doctor">Select Doctor</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                        {doctors.map((doctor) => (
                          <div
                            key={doctor.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                              selectedDoctor === doctor.id
                                ? 'bg-primary text-primary-foreground'
                                : 'hover:bg-muted'
                            }`}
                            onClick={() => setSelectedDoctor(doctor.id)}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${
                                selectedDoctor === doctor.id
                                  ? 'bg-primary-foreground text-primary'
                                  : 'bg-healwise-blue'
                              }`}>
                                {doctor.name.charAt(0)}
                              </div>
                              <div>
                                <p className="font-medium">Dr. {doctor.name}</p>
                                <p className={`text-sm ${
                                  selectedDoctor === doctor.id
                                    ? 'text-primary-foreground/70'
                                    : 'text-muted-foreground'
                                }`}>
                                  {doctor.specialty}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <Button 
                        onClick={() => setStep(2)} 
                        disabled={!selectedDoctor}
                      >
                        Next: Select Date & Time
                      </Button>
                    </div>
                  </div>
                )}
                
                {step === 2 && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <Label className="mb-2 block">Select Date</Label>
                        <div className="border rounded-md p-2">
                          <Calendar
                            mode="single"
                            selected={selectedDate}
                            onSelect={setSelectedDate}
                            fromDate={new Date()}
                            className="rounded-md"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label className="mb-2 block">Select Time</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {availableTimes.map((time) => (
                            <Button
                              key={time}
                              variant={selectedTime === time ? "default" : "outline"}
                              className="justify-center"
                              onClick={() => setSelectedTime(time)}
                            >
                              {time}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="notes">Notes (Optional)</Label>
                      <Textarea
                        id="notes"
                        placeholder="Reason for visit or additional information"
                        value={appointmentNotes}
                        onChange={(e) => setAppointmentNotes(e.target.value)}
                      />
                    </div>
                    
                    <div className="flex justify-between">
                      <Button 
                        variant="outline" 
                        onClick={() => setStep(1)}
                      >
                        Back: Select Doctor
                      </Button>
                      
                      <Button 
                        onClick={() => setStep(3)} 
                        disabled={!selectedDate || !selectedTime}
                      >
                        Next: Review & Confirm
                      </Button>
                    </div>
                  </div>
                )}
                
                {step === 3 && (
                  <div className="space-y-6">
                    <div className="bg-muted p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Appointment Summary</h3>
                      
                      <div className="space-y-4">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between pb-3 border-b">
                          <div className="flex items-center gap-2">
                            <User className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium">Doctor</span>
                          </div>
                          <span>
                            {(() => {
                              const doctor = getDoctorById(selectedDoctor);
                              return `Dr. ${doctor?.name} (${doctor?.specialty})`;
                            })()}
                          </span>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between pb-3 border-b">
                          <div className="flex items-center gap-2">
                            <CalendarDays className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium">Date</span>
                          </div>
                          <span>
                            {selectedDate?.toLocaleDateString(undefined, { 
                              weekday: 'long', 
                              year: 'numeric', 
                              month: 'long', 
                              day: 'numeric' 
                            })}
                          </span>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between pb-3 border-b">
                          <div className="flex items-center gap-2">
                            <Clock className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium">Time</span>
                          </div>
                          <span>{selectedTime}</span>
                        </div>
                        
                        {appointmentNotes && (
                          <div className="flex flex-col sm:flex-row justify-between pb-3 border-b">
                            <div className="flex items-start gap-2">
                              <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                              <span className="font-medium">Notes</span>
                            </div>
                            <span className="mt-2 sm:mt-0 sm:text-right">{appointmentNotes}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <Bell className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-blue-900">Appointment Reminders</h3>
                          <p className="text-sm text-blue-800 mt-1">
                            You will receive reminders 1 day before and 1 hour before your appointment.
                            You can customize your reminders after scheduling.
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <Button 
                        variant="outline" 
                        onClick={() => setStep(2)}
                      >
                        Back: Select Date & Time
                      </Button>
                      
                      <Button onClick={handleScheduleAppointment}>
                        Confirm & Schedule
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default AppointmentsPage;
